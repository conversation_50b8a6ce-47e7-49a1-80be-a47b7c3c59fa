/* Enhanced 2025 Modern Animations */

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1) rotate(0deg);
  }
  25% {
    transform: translate(30px, -50px) scale(1.1) rotate(90deg);
  }
  50% {
    transform: translate(-20px, 20px) scale(0.9) rotate(180deg);
  }
  75% {
    transform: translate(20px, 30px) scale(1.05) rotate(270deg);
  }
  100% {
    transform: translate(0px, 0px) scale(1) rotate(360deg);
  }
}

.animate-blob {
  animation: blob 12s ease-in-out infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 8s linear infinite;
}

/* Modern Glow Pulse Animation */
@keyframes glow-pulse {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.animate-glow-pulse {
  animation: glow-pulse 3s ease-in-out infinite;
}

@keyframes beam {
  0% {
    opacity: 0.3;
    transform: translateY(0) rotate(var(--rotation));
  }
  50% {
    opacity: 0.6;
    transform: translateY(-30px) rotate(var(--rotation));
  }
  100% {
    opacity: 0.3;
    transform: translateY(0) rotate(var(--rotation));
  }
}

.animate-beam {
  --rotation: 35deg;
  animation: beam 8s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-15px) rotate(2deg);
  }
  66% {
    transform: translateY(-5px) rotate(-1deg);
  }
  100% {
    transform: translateY(0px) rotate(0deg);
  }
}

.animate-float {
  animation: float 8s ease-in-out infinite;
}

@keyframes gradient-xy {
  0% {
    background-position: 0% 0%;
  }
  25% {
    background-position: 100% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  75% {
    background-position: 0% 100%;
  }
  100% {
    background-position: 0% 0%;
  }
}

.animate-gradient-xy {
  animation: gradient-xy 20s ease infinite;
  background-size: 400% 400%;
}

/* Modern 2025 Animations */

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

@keyframes bounce-gentle {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-10px) scale(1.02);
  }
}

.animate-bounce-gentle {
  animation: bounce-gentle 3s ease-in-out infinite;
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slide-up 0.6s ease-out;
}

@keyframes fade-in-scale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in-scale {
  animation: fade-in-scale 0.5s ease-out;
}

@keyframes text-glow {
  0%, 100% {
    text-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    text-shadow: 0 0 20px rgba(59, 130, 246, 0.6), 0 0 30px rgba(147, 51, 234, 0.4);
  }
}

.animate-text-glow {
  animation: text-glow 3s ease-in-out infinite;
}

@keyframes morph {
  0%, 100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  }
  50% {
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
  }
}

.animate-morph {
  animation: morph 8s ease-in-out infinite;
}

.perspective-1000 {
  perspective: 1000px;
}

/* Glass morphism utilities */
.glass-effect {
  backdrop-filter: blur(16px) saturate(180%);
  background-color: rgba(17, 25, 40, 0.75);
  border: 1px solid rgba(255, 255, 255, 0.125);
}

/* Modern shadow utilities */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05);
}

.shadow-glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), 0 0 40px rgba(147, 51, 234, 0.2);
}

/* Gradient text utilities */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Modern 2025 Navbar Animations */
@keyframes navbar-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(34, 211, 238, 0.1), 0 0 40px rgba(147, 51, 234, 0.05);
  }
  50% {
    box-shadow: 0 0 30px rgba(34, 211, 238, 0.2), 0 0 60px rgba(147, 51, 234, 0.1);
  }
}

.animate-navbar-glow {
  animation: navbar-glow 4s ease-in-out infinite;
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.animate-ripple {
  animation: ripple 0.6s linear;
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in-right {
  animation: slide-in-right 0.5s ease-out;
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.4;
    filter: blur(2px);
  }
  50% {
    opacity: 0.8;
    filter: blur(4px);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Modern glass morphism effect */
.glass-morphism {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Enhanced gradient animations */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient-shift {
  animation: gradient-shift 8s ease infinite;
  background-size: 200% 200%;
}
