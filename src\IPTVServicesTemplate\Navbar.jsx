
// src/components/Navbar.js
import React, { useState, useEffect } from 'react';
import { NavLink } from 'react-router-dom';
import { Menu, X, Sparkles } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import logo from "../assets/logo1.png";
import './animations.css';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeIndex, setActiveIndex] = useState(null);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navLinkStyles = ({ isActive }) =>
    `relative px-6 py-3 transition-all duration-500 ease-out group font-medium tracking-wide
    ${isActive
      ? 'text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 font-semibold'
      : 'text-slate-300 hover:text-white'}
    hover:scale-105 hover:shadow-lg hover:shadow-cyan-500/20`;
  
  const handleMouseEnter = (index) => {
    setActiveIndex(index);
  };
  
  const handleMouseLeave = () => {
    setActiveIndex(null);
  };

  return (
    <motion.nav
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-700 ${
        isScrolled
          ? 'backdrop-blur-xl shadow-2xl shadow-black/20 border-b border-cyan-500/20'
          : 'backdrop-blur-md'
      }`}
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      style={{
        background: isScrolled
          ? 'linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 50%, rgba(15, 23, 42, 0.95) 100%)'
          : 'linear-gradient(135deg, rgba(15, 23, 42, 0.1) 0%, rgba(30, 41, 59, 0.2) 50%, rgba(15, 23, 42, 0.1) 100%)'
      }}
    >
      {/* Animated background gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/5 via-blue-500/5 to-purple-500/5 animate-gradient-xy opacity-50"></div>

      <div className="container mx-auto px-6 relative">
        <div className="flex items-center justify-between h-24">
          {/* Logo with enhanced animation and glow effect */}
          <motion.div
            className="flex items-center relative group"
            whileHover={{ scale: 1.08 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-cyan-400/20 to-blue-500/20 rounded-xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <img
              src={logo}
              alt="Logo"
              className="h-12 w-auto filter drop-shadow-2xl relative z-10 transition-all duration-500 group-hover:drop-shadow-[0_0_20px_rgba(34,211,238,0.4)]"
            />
            <Sparkles className="absolute -top-1 -right-1 w-4 h-4 text-cyan-400 opacity-0 group-hover:opacity-100 transition-opacity duration-500 animate-pulse" />
          </motion.div>
          
          {/* Desktop Navigation with enhanced modern animations */}
          <div className="hidden md:flex items-center space-x-2">
            {[
              { to: "/", text: "Home", icon: "🏠" },
              { to: "/features", text: "Features", icon: "✨" },
              { to: "/packages", text: "Packages", icon: "📦" },
              { to: "/faq", text: "FAQ", icon: "❓" },
              { to: "/contact", text: "Contact", icon: "📞" }
            ].map((link, index) => (
              <motion.div
                key={link.to}
                className="relative group"
                onMouseEnter={() => handleMouseEnter(index)}
                onMouseLeave={handleMouseLeave}
                whileHover={{ y: -2 }}
                transition={{ duration: 0.2 }}
              >
                {/* Hover background effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/10 via-blue-500/10 to-purple-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-500 blur-sm"></div>
                <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/5 via-blue-500/5 to-purple-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-300"></div>

                <NavLink
                  to={link.to}
                  className={navLinkStyles}
                >
                  <span className="flex items-center gap-2 relative z-10">
                    <span className="text-sm opacity-70 group-hover:opacity-100 transition-opacity duration-300">{link.icon}</span>
                    {link.text}
                  </span>

                  {/* Modern underline animation */}
                  <motion.div
                    className="absolute inset-x-2 -bottom-1 h-0.5 bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 rounded-full"
                    initial={{ scaleX: 0, opacity: 0 }}
                    animate={{
                      scaleX: activeIndex === index ? 1 : 0,
                      opacity: activeIndex === index ? 1 : 0
                    }}
                    transition={{ duration: 0.4, ease: "easeOut" }}
                  />

                  {/* Glow effect on hover */}
                  <motion.div
                    className="absolute inset-x-2 -bottom-1 h-0.5 bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 rounded-full blur-sm"
                    initial={{ scaleX: 0, opacity: 0 }}
                    animate={{
                      scaleX: activeIndex === index ? 1 : 0,
                      opacity: activeIndex === index ? 0.6 : 0
                    }}
                    transition={{ duration: 0.4, ease: "easeOut" }}
                  />
                </NavLink>
              </motion.div>
            ))}
          </div>

          {/* Mobile Menu Button with modern glass morphism */}
          <motion.button
            className="md:hidden relative p-4 rounded-2xl bg-gradient-to-r from-cyan-500/10 via-blue-500/10 to-purple-500/10 text-cyan-400 border border-white/20 backdrop-blur-xl shadow-2xl hover:shadow-cyan-500/30 transition-all duration-500 group overflow-hidden"
            onClick={() => setIsOpen(!isOpen)}
            aria-label="Toggle menu"
            whileHover={{ scale: 1.08 }}
            whileTap={{ scale: 0.92 }}
          >
            {/* Animated background */}
            <div className="absolute inset-0 bg-gradient-to-r from-cyan-400/5 to-purple-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

            <motion.div
              animate={{ rotate: isOpen ? 180 : 0 }}
              transition={{ duration: 0.5, ease: "easeInOut" }}
              className="relative z-10"
            >
              {isOpen ? (
                <X size={24} className="transform transition-all duration-500 text-cyan-300 group-hover:text-white" />
              ) : (
                <Menu size={24} className="transform transition-all duration-500 text-cyan-300 group-hover:text-white" />
              )}
            </motion.div>

            {/* Ripple effect */}
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-cyan-400/20 to-purple-400/20 opacity-0 group-active:opacity-100 transition-opacity duration-200"></div>
          </motion.button>
        </div>

        {/* Enhanced Mobile Menu with modern glass morphism */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ height: 0, opacity: 0, y: -20 }}
              animate={{ height: 'auto', opacity: 1, y: 0 }}
              exit={{ height: 0, opacity: 0, y: -20 }}
              transition={{ duration: 0.5, ease: "easeOut" }}
              className="md:hidden overflow-hidden"
            >
              <div className="py-6 space-y-2 border-t border-gradient-to-r border-cyan-500/20 bg-gradient-to-b from-slate-800/50 to-slate-900/50 backdrop-blur-xl rounded-b-3xl mx-4 mt-4">
                {[
                  { to: "/", text: "Home", icon: "🏠" },
                  { to: "/features", text: "Features", icon: "✨" },
                  { to: "/packages", text: "Packages", icon: "📦" },
                  { to: "/faq", text: "FAQ", icon: "❓" },
                  { to: "/contact", text: "Contact", icon: "📞" }
                ].map((link, index) => (
                  <motion.div
                    key={link.to}
                    initial={{ x: -30, opacity: 0, scale: 0.9 }}
                    animate={{ x: 0, opacity: 1, scale: 1 }}
                    transition={{
                      delay: index * 0.1,
                      duration: 0.4,
                      ease: "easeOut"
                    }}
                    className="relative group"
                  >
                    {/* Mobile link background effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/5 via-blue-500/5 to-purple-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-500 blur-sm"></div>

                    <NavLink
                      to={link.to}
                      className="relative block px-6 py-4 mx-2 rounded-2xl text-slate-300 hover:text-white font-medium tracking-wide transition-all duration-500 group-hover:scale-105 group-hover:shadow-lg group-hover:shadow-cyan-500/10"
                      onClick={() => setIsOpen(false)}
                    >
                      <div className="flex items-center gap-3 relative z-10">
                        <span className="text-lg opacity-70 group-hover:opacity-100 transition-opacity duration-300">{link.icon}</span>
                        <span className="text-base">{link.text}</span>
                      </div>

                      {/* Mobile link underline */}
                      <motion.div
                        className="absolute bottom-2 left-6 right-6 h-0.5 bg-gradient-to-r from-cyan-400 to-purple-600 rounded-full opacity-0 group-hover:opacity-100"
                        initial={{ scaleX: 0 }}
                        whileHover={{ scaleX: 1 }}
                        transition={{ duration: 0.3 }}
                      />
                    </NavLink>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.nav>
  );
};

export default Navbar;

