
// Modern IPTV Navbar Component - 2025 Design
import React, { useState, useEffect } from 'react';
import { NavLink } from 'react-router-dom';
import { Menu, X, Home, Zap, Package, HelpCircle, Phone } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import logo from "../assets/logo1.png";
import './animations.css';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [hoveredItem, setHoveredItem] = useState(null);

  // Navigation items with modern icons
  const navigationItems = [
    { to: "/", text: "Home", icon: Home },
    { to: "/features", text: "Features", icon: Zap },
    { to: "/packages", text: "Packages", icon: Package },
    { to: "/faq", text: "FAQ", icon: HelpCircle },
    { to: "/contact", text: "Contact", icon: Phone }
  ];

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 30);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (isOpen) setIsOpen(false);
    };
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [isOpen]);

  return (
    <motion.header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
        isScrolled
          ? 'bg-slate-900/95 backdrop-blur-xl shadow-2xl border-b border-slate-700/50'
          : 'bg-transparent backdrop-blur-sm'
      }`}
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      {/* Modern gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-purple-600/5 to-cyan-600/5"></div>

      <nav className="relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">

            {/* Logo Section */}
            <motion.div
              className="flex items-center space-x-3 group"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl blur-lg opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
                <img
                  src={logo}
                  alt="IPTV Logo"
                  className="h-10 w-auto relative z-10 transition-all duration-300 group-hover:brightness-110"
                />
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  IPTV Services
                </h1>
              </div>
            </motion.div>
          
            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-1">
              {navigationItems.map((item, index) => {
                const IconComponent = item.icon;
                return (
                  <motion.div
                    key={item.to}
                    className="relative"
                    onMouseEnter={() => setHoveredItem(index)}
                    onMouseLeave={() => setHoveredItem(null)}
                    whileHover={{ y: -1 }}
                    transition={{ duration: 0.2 }}
                  >
                    <NavLink
                      to={item.to}
                      className={({ isActive }) =>
                        `relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 group ${
                          isActive
                            ? 'text-white bg-gradient-to-r from-blue-500/20 to-purple-500/20 shadow-lg'
                            : 'text-slate-300 hover:text-white hover:bg-slate-800/50'
                        }`
                      }
                    >
                      <IconComponent size={18} className="transition-all duration-300 group-hover:scale-110" />
                      <span className="text-sm font-medium">{item.text}</span>

                      {/* Active indicator */}
                      <motion.div
                        className="absolute bottom-0 left-1/2 w-1 h-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full"
                        initial={{ scale: 0, x: '-50%' }}
                        animate={{ scale: hoveredItem === index ? 1 : 0 }}
                        transition={{ duration: 0.2 }}
                      />
                    </NavLink>
                  </motion.div>
                );
              })}
            </div>

          {/* Mobile Menu Button with modern glass morphism */}
          <motion.button
            className="md:hidden relative p-4 rounded-2xl bg-gradient-to-r from-cyan-500/10 via-blue-500/10 to-purple-500/10 text-cyan-400 border border-white/20 backdrop-blur-xl shadow-2xl hover:shadow-cyan-500/30 transition-all duration-500 group overflow-hidden"
            onClick={() => setIsOpen(!isOpen)}
            aria-label="Toggle menu"
            whileHover={{ scale: 1.08 }}
            whileTap={{ scale: 0.92 }}
          >
            {/* Animated background */}
            <div className="absolute inset-0 bg-gradient-to-r from-cyan-400/5 to-purple-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

            <motion.div
              animate={{ rotate: isOpen ? 180 : 0 }}
              transition={{ duration: 0.5, ease: "easeInOut" }}
              className="relative z-10"
            >
              {isOpen ? (
                <X size={24} className="transform transition-all duration-500 text-cyan-300 group-hover:text-white" />
              ) : (
                <Menu size={24} className="transform transition-all duration-500 text-cyan-300 group-hover:text-white" />
              )}
            </motion.div>

            {/* Ripple effect */}
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-cyan-400/20 to-purple-400/20 opacity-0 group-active:opacity-100 transition-opacity duration-200"></div>
          </motion.button>
        </div>

        {/* Enhanced Mobile Menu with modern glass morphism */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ height: 0, opacity: 0, y: -20 }}
              animate={{ height: 'auto', opacity: 1, y: 0 }}
              exit={{ height: 0, opacity: 0, y: -20 }}
              transition={{ duration: 0.5, ease: "easeOut" }}
              className="md:hidden overflow-hidden"
            >
              <div className="py-6 space-y-2 border-t border-gradient-to-r border-cyan-500/20 bg-gradient-to-b from-slate-800/50 to-slate-900/50 backdrop-blur-xl rounded-b-3xl mx-4 mt-4">
                {[
                  { to: "/", text: "Home", icon: "🏠" },
                  { to: "/features", text: "Features", icon: "✨" },
                  { to: "/packages", text: "Packages", icon: "📦" },
                  { to: "/faq", text: "FAQ", icon: "❓" },
                  { to: "/contact", text: "Contact", icon: "📞" }
                ].map((link, index) => (
                  <motion.div
                    key={link.to}
                    initial={{ x: -30, opacity: 0, scale: 0.9 }}
                    animate={{ x: 0, opacity: 1, scale: 1 }}
                    transition={{
                      delay: index * 0.1,
                      duration: 0.4,
                      ease: "easeOut"
                    }}
                    className="relative group"
                  >
                    {/* Mobile link background effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/5 via-blue-500/5 to-purple-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-500 blur-sm"></div>

                    <NavLink
                      to={link.to}
                      className="relative block px-6 py-4 mx-2 rounded-2xl text-slate-300 hover:text-white font-medium tracking-wide transition-all duration-500 group-hover:scale-105 group-hover:shadow-lg group-hover:shadow-cyan-500/10"
                      onClick={() => setIsOpen(false)}
                    >
                      <div className="flex items-center gap-3 relative z-10">
                        <span className="text-lg opacity-70 group-hover:opacity-100 transition-opacity duration-300">{link.icon}</span>
                        <span className="text-base">{link.text}</span>
                      </div>

                      {/* Mobile link underline */}
                      <motion.div
                        className="absolute bottom-2 left-6 right-6 h-0.5 bg-gradient-to-r from-cyan-400 to-purple-600 rounded-full opacity-0 group-hover:opacity-100"
                        initial={{ scaleX: 0 }}
                        whileHover={{ scaleX: 1 }}
                        transition={{ duration: 0.3 }}
                      />
                    </NavLink>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.nav>
    
  );
};

export default Navbar;

